{"name": "redux", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"bootstrap": "^5.3.8", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "vite": "^7.1.7"}}