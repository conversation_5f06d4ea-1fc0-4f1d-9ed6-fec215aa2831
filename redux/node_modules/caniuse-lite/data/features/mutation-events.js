module.exports={A:{A:{"2":"K D E vC","260":"F A B"},B:{"2":"TB UB VB WB I","66":"JB KB LB MB NB OB PB QB RB SB","132":"0 1 2 3 4 5 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB","260":"C L M G N O P"},C:{"2":"wC SC J XB WB I WC LC XC xC yC zC 0C","260":"0 1 2 3 4 5 6 7 8 9 K D E F A B C L M G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B TC 3B UC 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC Q H R VC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB"},D:{"2":"RB SB TB UB VB WB I WC LC XC","16":"J XB K D E F A B C L M","66":"JB KB LB MB NB OB PB QB","132":"0 1 2 3 4 5 6 7 8 9 G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B TC 3B UC 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB"},E:{"2":"rC sC CD","16":"1C YC","132":"J XB K D E F A B C L M G 2C 3C 4C 5C ZC MC NC 6C 7C 8C aC bC OC 9C PC cC dC eC fC gC AD QC hC iC jC kC lC BD RC mC nC oC pC qC"},F:{"1":"C HD NC","2":"F DD ED FD GD","16":"B MC tC","66":"0 1 2 3 4 5 w x y z","132":"6 7 8 9 G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC Q H R VC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v"},G:{"2":"rC sC","16":"YC ID","132":"E uC JD KD LD MD ND OD PD QD RD SD TD UD VD WD XD YD ZD aD bD aC bC OC cD PC cC dC eC fC gC dD QC hC iC jC kC lC eD RC mC nC oC pC qC"},H:{"2":"fD"},I:{"2":"I","16":"gD hD","132":"SC J iD jD uC kD lD"},J:{"132":"D A"},K:{"1":"C NC","2":"A","16":"B MC tC","132":"H"},L:{"2":"I"},M:{"2":"LC"},N:{"260":"A B"},O:{"132":"OC"},P:{"132":"6 7 8 9 J AB BB CB DB EB mD nD oD pD qD ZC rD sD tD uD vD PC QC RC wD"},Q:{"132":"xD"},R:{"132":"yD"},S:{"260":"zD 0D"}},B:7,C:"Mutation events",D:true};
