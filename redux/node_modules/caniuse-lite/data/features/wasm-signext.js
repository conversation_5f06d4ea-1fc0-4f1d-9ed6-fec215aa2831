module.exports={A:{A:{"2":"K D E F A B vC"},B:{"1":"0 1 2 3 4 5 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB I","2":"C L M G N O P"},C:{"1":"0 1 2 3 4 5 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC Q H R VC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB I WC LC XC xC yC","2":"6 7 8 9 wC SC J XB K D E F A B C L M G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B TC 3B UC zC 0C"},D:{"1":"0 1 2 3 4 5 GC HC IC JC KC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB I WC LC XC","2":"6 7 8 9 J XB K D E F A B C L M G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B TC 3B UC 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC"},E:{"1":"G 7C 8C aC bC OC 9C PC cC dC eC fC gC AD QC hC iC jC kC lC BD RC mC nC oC pC qC rC sC CD","2":"J XB K D E F A B C L M 1C YC 2C 3C 4C 5C ZC MC NC 6C"},F:{"1":"0 1 2 3 4 5 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC GC HC IC JC KC Q H R VC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z","2":"6 7 8 9 F B C G N O P YB AB BB CB DB EB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B DD ED FD GD MC tC HD NC"},G:{"1":"aD bD aC bC OC cD PC cC dC eC fC gC dD QC hC iC jC kC lC eD RC mC nC oC pC qC rC sC","2":"E YC ID uC JD KD LD MD ND OD PD QD RD SD TD UD VD WD XD YD ZD"},H:{"2":"fD"},I:{"1":"I","2":"SC J gD hD iD jD uC kD lD"},J:{"2":"D A"},K:{"1":"H","2":"A B C MC tC NC"},L:{"1":"I"},M:{"1":"LC"},N:{"2":"A B"},O:{"1":"OC"},P:{"1":"6 7 8 9 AB BB CB DB EB rD sD tD uD vD PC QC RC wD","2":"J mD nD oD pD qD ZC"},Q:{"16":"xD"},R:{"16":"yD"},S:{"2":"zD","16":"0D"}},B:5,C:"WebAssembly Sign Extension Operators",D:true};
